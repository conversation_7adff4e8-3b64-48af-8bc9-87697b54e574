# Odude Chat - AI-Powered Q&A Collections SaaS

A modern, multilingual chatbot application built with Next.js 15, featuring RAG (Retrieval-Augmented Generation) capabilities, vector search, and n8n integration. The application supports Nepali-English conversational tone and is designed for sales-oriented interactions.

## 🚀 Features

### Core Functionality
- **Multi-user Q&A Collections**: Create and manage collections of question-answer pairs
- **RAG-powered Chatbot**: AI responses based on your Q&A collection context
- **Vector Search**: Semantic similarity search using Qdrant vector database
- **Multilingual Support**: English, Hindi-English, and Nepali-English responses
- **Sales-oriented Persona**: WhatsApp-style short messages with purchase-encouraging questions

### AI & Integration
- **OpenAI Compatible API**: Easy switching between AI providers (currently using Gemini)
- **n8n Integration**: Public API endpoints for workflow automation
- **Multiple Embedding Providers**: Gemini and OpenRouter support
- **Real-time Status Monitoring**: Comprehensive health checks and testing endpoints

### Authentication & Security
- **Google OAuth**: Secure authentication via NextAuth.js
- **User-specific Data**: Each user can only access their own collections
- **API Key Protection**: Secure n8n endpoints with API key authentication

## 🛠 Tech Stack

- **Frontend**: Next.js 15, <PERSON>act 19, Mantine UI, TypeScript
- **Backend**: Next.js API Routes, NextAuth.js
- **Database**: Supabase (PostgreSQL)
- **Vector Database**: Qdrant
- **AI/LLM**: Google Gemini (OpenAI compatible)
- **Styling**: Tailwind CSS
- **Deployment**: Vercel

## 📋 Prerequisites

- Node.js 18+ and npm
- Supabase account and project
- Qdrant instance (cloud or self-hosted)
- Google OAuth credentials
- Gemini API key

## ⚙️ Environment Setup

Create a `.env.local` file with the following variables:

```bash
# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Supabase
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key

# Qdrant Vector Database
QDRANT_URL=your-qdrant-url
QDRANT_API_KEY=your-qdrant-api-key

# AI Provider
GEMINI_API_KEY=your-gemini-api-key

# n8n Integration
N8N_API_KEY=your-n8n-api-key

# Admin Access
ADMIN_EMAIL=<EMAIL>

# Internal API Token (for server-to-server calls)
INTERNAL_API_TOKEN=your-internal-token
```

## 🚀 Installation & Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd odude-chat
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   - Copy `.env.local.example` to `.env.local`
   - Fill in all required environment variables

4. **Set up Supabase database**
   - Create tables for `users`, `collections`, and `qa_pairs`
   - Enable Row Level Security (RLS)
   - Set up authentication policies

5. **Run the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📖 Usage

### For End Users

1. **Sign in** with your Google account
2. **Create Collections** to organize your Q&A pairs
3. **Add Q&A Pairs** to build your knowledge base
4. **Chat with AI** that uses your Q&A context for responses
5. **Test RAG functionality** with the built-in testing tools

### For Developers & n8n Users

Access the public API endpoints for integration:

- **n8n Chat**: `POST /api/n8n/rag-chat`
- **n8n Search**: `POST /api/n8n/rag-search`
- **Collection Info**: `GET /api/n8n/{collection_id}`
- **Status Check**: `GET /api/n8n/status`
- **Documentation**: `GET /api/n8n/docs`

## 🔌 API Endpoints

### Public Endpoints (No Authentication)
- `GET /api/status` - System health and status
- `GET /api/n8n/docs` - n8n API documentation
- `GET /api/n8n/{collection_id}` - Collection information and testing

### Authenticated Endpoints
- `POST /api/rag-chat` - Generate AI responses with RAG
- `POST /api/rag-search` - Search similar Q&A pairs
- `GET /api/collections` - Get user's collections
- `POST /api/qdrant-sync` - Sync Q&A pairs to vector database

### n8n Integration Endpoints (API Key Required)
- `POST /api/n8n/rag-chat` - n8n compatible chat endpoint
- `POST /api/n8n/rag-search` - n8n compatible search endpoint
- `GET /api/n8n/status` - Service status for n8n workflows

## 🤖 Chatbot Features

### Conversational Style
- **Nepali-English Tone**: Mixed language responses for natural communication
- **WhatsApp-style Messages**: Short, concise, and friendly responses
- **Sales Persona**: Asks relevant questions to encourage purchases
- **Context-aware**: Uses your Q&A collection for accurate responses

### Language Support
- **English** (`en`): Pure English responses
- **Hindi-English** (`hi-en`): Mixed Hindi and English responses
- **Nepali-English** (`ne-en`): Mixed Nepali and English responses (default)

### Example Chat Interaction
```
User: "What material is this made of?"
Bot: "यो nylon को बनेको छ, cotton होइन। Back side transparent छ र front double layer छ। के तपाईंलाई यसको quality बारे अरू केही जान्न चाहनुहुन्छ?"
```

## 🧪 Testing & Development

The application includes comprehensive testing endpoints:

### Health Checks
- `GET /api/status` - Overall system status
- `GET /api/test` - Database connectivity test
- `GET /api/test-auth` - Authentication test

### RAG Workflow Tests
- `GET /api/test-rag-chat` - Complete RAG workflow test
- `GET /api/test-embedding-status` - Embedding generation test
- `GET /api/test-multilingual-embedding` - Multilingual support test
- `GET /api/test-complete-rag-workflow` - End-to-end workflow test

### n8n Integration Tests
- `GET /api/n8n/status` - n8n service health (requires API key)
- Test collection endpoints for workflow validation

## 🚀 Deployment

### Vercel Deployment (Recommended)

1. **Connect your repository** to Vercel
2. **Set environment variables** in Vercel dashboard
3. **Deploy** - Vercel will automatically build and deploy

### Manual Deployment

1. **Build the application**
   ```bash
   npm run build
   ```

2. **Start production server**
   ```bash
   npm start
   ```

### Environment Variables for Production
Ensure all environment variables are set in your production environment:
- Database connections (Supabase, Qdrant)
- API keys (Gemini, Google OAuth)
- Authentication secrets
- n8n integration keys

## 🔧 Configuration

### AI Provider Configuration
The application is designed to be OpenAI compatible. You can easily switch between providers by updating `src/lib/config.ts`:

```typescript
// Current configuration (Gemini)
api: {
  chat: {
    provider: 'gemini',
    model: 'gemini-1.5-flash',
    apiKey: process.env.GEMINI_API_KEY,
  }
}

// Switch to OpenAI
api: {
  chat: {
    provider: 'openai',
    model: 'gpt-4',
    apiKey: process.env.OPENAI_API_KEY,
  }
}
```

### Chatbot Personality
Customize the chatbot's personality in the RAG prompt templates:
- Sales-oriented questions
- Nepali-English mixed responses
- WhatsApp-style short messages
- Context-aware recommendations

## 📊 Monitoring & Analytics

### Built-in Status Dashboard
Access `/dashboard/status` for:
- System health monitoring
- API endpoint testing
- Database connectivity checks
- Vector search performance
- Embedding generation status

### n8n Integration Status
Monitor n8n integration health:
- Service availability
- API key validation
- Collection accessibility
- Response time metrics

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Check the `/api/status` endpoint for system health
- Review the `/api/n8n/docs` for API documentation
- Test individual components using the built-in test endpoints
- Monitor logs for debugging information

## 🔮 Roadmap

- [ ] Additional AI provider integrations (OpenAI, Claude)
- [ ] Advanced analytics and usage tracking
- [ ] Bulk Q&A import/export functionality
- [ ] Advanced chatbot personality customization
- [ ] Multi-language UI support
- [ ] Enhanced n8n workflow templates
