import { NextRequest, NextResponse } from 'next/server';
import { createA<PERSON>rovider, type ChatCompletionRequest } from '@/lib/ai-providers';
import { getCurrentUser } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const user = await getCurrentUser();
    
    const body: ChatCompletionRequest = await request.json();
    
    // Validate required fields
    if (!body.messages || !Array.isArray(body.messages) || body.messages.length === 0) {
      return NextResponse.json(
        { 
          error: {
            message: 'Missing required field: messages',
            type: 'invalid_request_error',
            code: 'missing_required_parameter'
          }
        },
        { status: 400 }
      );
    }

    // Validate message format
    for (const message of body.messages) {
      if (!message.role || !message.content) {
        return NextResponse.json(
          { 
            error: {
              message: 'Each message must have role and content fields',
              type: 'invalid_request_error',
              code: 'invalid_message_format'
            }
          },
          { status: 400 }
        );
      }

      if (!['system', 'user', 'assistant'].includes(message.role)) {
        return NextResponse.json(
          { 
            error: {
              message: 'Message role must be one of: system, user, assistant',
              type: 'invalid_request_error',
              code: 'invalid_message_role'
            }
          },
          { status: 400 }
        );
      }
    }

    // Set default values
    const completionRequest: ChatCompletionRequest = {
      model: body.model || 'gemini-1.5-flash',
      messages: body.messages,
      temperature: body.temperature ?? 0.7,
      max_tokens: body.max_tokens ?? 1000,
      stream: body.stream ?? false
    };

    console.log('🤖 OpenAI-compatible chat completion request:', {
      user_id: user.id,
      model: completionRequest.model,
      messages_count: completionRequest.messages.length,
      temperature: completionRequest.temperature,
      max_tokens: completionRequest.max_tokens
    });

    // Create AI provider and generate response
    const aiProvider = createAIProvider();
    const response = await aiProvider.generateChatCompletion(completionRequest);

    console.log('✅ Chat completion generated:', {
      response_length: response.choices[0]?.message.content.length || 0,
      finish_reason: response.choices[0]?.finish_reason,
      total_tokens: response.usage.total_tokens
    });

    return NextResponse.json(response);

  } catch (error) {
    console.error('❌ Chat completion error:', error);

    // Handle specific error types
    if (error instanceof Error) {
      if (error.message.includes('API key')) {
        return NextResponse.json(
          { 
            error: {
              message: 'Invalid API key or AI service unavailable',
              type: 'authentication_error',
              code: 'invalid_api_key'
            }
          },
          { status: 401 }
        );
      }

      if (error.message.includes('Unsupported AI provider')) {
        return NextResponse.json(
          { 
            error: {
              message: error.message,
              type: 'invalid_request_error',
              code: 'unsupported_provider'
            }
          },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      { 
        error: {
          message: 'Internal server error',
          type: 'server_error',
          code: 'internal_error'
        }
      },
      { status: 500 }
    );
  }
}

// GET endpoint for API information
export async function GET() {
  return NextResponse.json({
    name: 'OpenAI-compatible Chat Completions API',
    version: '1.0.0',
    description: 'OpenAI-compatible chat completions endpoint that works with multiple AI providers',
    endpoint: '/api/v1/chat/completions',
    methods: ['POST'],
    authentication: 'Required (NextAuth session)',
    supported_providers: ['gemini', 'openai'],
    current_provider: process.env.NODE_ENV === 'development' ? 'gemini' : 'configured_provider',
    example_request: {
      model: 'gemini-1.5-flash',
      messages: [
        {
          role: 'system',
          content: 'You are a helpful assistant.'
        },
        {
          role: 'user',
          content: 'Hello, how are you?'
        }
      ],
      temperature: 0.7,
      max_tokens: 1000
    },
    example_response: {
      id: 'chatcmpl-123',
      object: 'chat.completion',
      created: **********,
      model: 'gemini-1.5-flash',
      choices: [
        {
          index: 0,
          message: {
            role: 'assistant',
            content: 'Hello! I\'m doing well, thank you for asking. How can I help you today?'
          },
          finish_reason: 'stop'
        }
      ],
      usage: {
        prompt_tokens: 20,
        completion_tokens: 18,
        total_tokens: 38
      }
    }
  });
}
