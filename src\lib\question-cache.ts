/**
 * Question Cache System
 * Caches common repetitive questions to save embedding costs and provide faster responses
 */

interface CachedResponse {
  answer: string;
  language: string;
  timestamp: number;
  hitCount: number;
}

interface QuestionPattern {
  patterns: string[];
  cacheKey: string;
  description: string;
}

// Common question patterns that can be cached
const COMMON_QUESTION_PATTERNS: QuestionPattern[] = [
  {
    patterns: ['how much', 'price', 'cost', 'kati', 'kitna', 'pp', 'rate', 'amount'],
    cacheKey: 'price_inquiry',
    description: 'Price and cost related questions'
  },
  {
    patterns: ['quality', 'good', 'best', 'gunas', 'kasto', 'kaisa'],
    cacheKey: 'quality_inquiry',
    description: 'Quality related questions'
  },
  {
    patterns: ['available', 'stock', 'cha', 'hai', 'milcha', 'milta'],
    cacheKey: 'availability_inquiry',
    description: 'Availability and stock questions'
  },
  {
    patterns: ['material', 'made', 'fabric', 'cloth', 'kapada', 'baneko'],
    cacheKey: 'material_inquiry',
    description: 'Material and composition questions'
  },
  {
    patterns: ['size', 'dimension', 'length', 'width', 'height', 'saiz'],
    cacheKey: 'size_inquiry',
    description: 'Size and dimension questions'
  },
  {
    patterns: ['color', 'colour', 'rang', 'shade'],
    cacheKey: 'color_inquiry',
    description: 'Color related questions'
  },
  {
    patterns: ['delivery', 'shipping', 'transport', 'pathau', 'bhej'],
    cacheKey: 'delivery_inquiry',
    description: 'Delivery and shipping questions'
  }
];

// In-memory cache (in production, you might want to use Redis or similar)
const questionCache = new Map<string, CachedResponse>();

/**
 * Generate cache key for a question based on collection and question pattern
 */
function generateCacheKey(collectionId: string, questionPattern: string, language: string): string {
  return `${collectionId}:${questionPattern}:${language}`;
}

/**
 * Detect if a question matches common patterns
 */
export function detectQuestionPattern(question: string): string | null {
  const lowerQuestion = question.toLowerCase();
  
  for (const pattern of COMMON_QUESTION_PATTERNS) {
    const hasPattern = pattern.patterns.some(p => lowerQuestion.includes(p));
    if (hasPattern) {
      return pattern.cacheKey;
    }
  }
  
  return null;
}

/**
 * Check if a cached response exists for this question pattern
 */
export function getCachedResponse(
  collectionId: string, 
  questionPattern: string, 
  language: string
): CachedResponse | null {
  const cacheKey = generateCacheKey(collectionId, questionPattern, language);
  const cached = questionCache.get(cacheKey);
  
  if (cached) {
    // Check if cache is still valid (24 hours)
    const isExpired = Date.now() - cached.timestamp > 24 * 60 * 60 * 1000;
    if (!isExpired) {
      // Increment hit count
      cached.hitCount++;
      return cached;
    } else {
      // Remove expired cache
      questionCache.delete(cacheKey);
    }
  }
  
  return null;
}

/**
 * Cache a response for a question pattern
 */
export function cacheResponse(
  collectionId: string,
  questionPattern: string,
  language: string,
  answer: string
): void {
  const cacheKey = generateCacheKey(collectionId, questionPattern, language);
  
  questionCache.set(cacheKey, {
    answer,
    language,
    timestamp: Date.now(),
    hitCount: 1
  });
}

/**
 * Get cache statistics
 */
export function getCacheStats(): {
  totalEntries: number;
  hitCounts: Record<string, number>;
  patterns: QuestionPattern[];
} {
  const hitCounts: Record<string, number> = {};
  
  for (const [key, value] of questionCache.entries()) {
    hitCounts[key] = value.hitCount;
  }
  
  return {
    totalEntries: questionCache.size,
    hitCounts,
    patterns: COMMON_QUESTION_PATTERNS
  };
}

/**
 * Clear expired cache entries
 */
export function clearExpiredCache(): number {
  let cleared = 0;
  const now = Date.now();
  const expireTime = 24 * 60 * 60 * 1000; // 24 hours
  
  for (const [key, value] of questionCache.entries()) {
    if (now - value.timestamp > expireTime) {
      questionCache.delete(key);
      cleared++;
    }
  }
  
  return cleared;
}

/**
 * Clear all cache entries
 */
export function clearAllCache(): void {
  questionCache.clear();
}

/**
 * Check if question is worth caching (simple heuristic)
 */
export function shouldCacheQuestion(question: string): boolean {
  // Cache short, common questions
  const wordCount = question.trim().split(/\s+/).length;
  return wordCount <= 10 && detectQuestionPattern(question) !== null;
}
