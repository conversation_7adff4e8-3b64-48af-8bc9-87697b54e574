# Deployment Guide for Odude Chat

## ✅ Build Status
The application builds successfully with Next.js 15. The build process completes without compilation errors.

## 🚀 Vercel Deployment

### Prerequisites
1. GitHub repository with the code
2. Vercel account
3. All required environment variables

### Environment Variables Required for Production

```bash
# NextAuth Configuration
NEXTAUTH_URL=https://your-domain.vercel.app
NEXTAUTH_SECRET=your-nextauth-secret-key

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Supabase
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key

# Qdrant Vector Database
QDRANT_URL=your-qdrant-url
QDRANT_API_KEY=your-qdrant-api-key

# AI Provider (Gemini)
GEMINI_API_KEY=your-gemini-api-key

# n8n Integration
N8N_API_KEY=your-n8n-api-key

# Admin Access
ADMIN_EMAIL=<EMAIL>

# Internal API Token
INTERNAL_API_TOKEN=your-secure-internal-token
```

### Deployment Steps

1. **Connect Repository to Vercel**
   - Go to [Vercel Dashboard](https://vercel.com/dashboard)
   - Click "New Project"
   - Import your GitHub repository

2. **Configure Environment Variables**
   - In Vercel project settings, go to "Environment Variables"
   - Add all the required environment variables listed above
   - Make sure to set them for Production, Preview, and Development environments

3. **Deploy**
   - Vercel will automatically build and deploy
   - The build process takes ~30-40 seconds
   - Check deployment logs for any issues

### Build Configuration
The application uses:
- **Framework**: Next.js 15.4.5
- **Node.js**: 18+ (recommended)
- **Build Command**: `npm run build`
- **Output Directory**: `.next`

## 🔧 Post-Deployment Setup

### 1. Update Google OAuth Settings
- Go to [Google Cloud Console](https://console.cloud.google.com/)
- Update OAuth redirect URIs to include your Vercel domain:
  - `https://your-domain.vercel.app/api/auth/callback/google`

### 2. Update Supabase Settings
- Update CORS settings in Supabase to allow your Vercel domain
- Verify RLS policies are properly configured

### 3. Test Deployment
Visit these endpoints to verify everything is working:
- `https://your-domain.vercel.app/api/status` - System health check
- `https://your-domain.vercel.app/api/v1/config` - API configuration
- `https://your-domain.vercel.app/dashboard` - Main application

## 🧪 Testing the Deployment

### Health Checks
1. **System Status**: `/api/status`
2. **Database Connection**: `/api/test`
3. **Authentication**: `/api/test-auth`
4. **RAG Functionality**: `/api/test-rag-chat`

### OpenAI-Compatible Endpoints
1. **Chat Completions**: `POST /api/v1/chat/completions`
2. **RAG Chat**: `POST /api/v1/chat/rag-completions`
3. **Configuration**: `GET /api/v1/config`

### n8n Integration
1. **Status**: `GET /api/n8n/status` (requires API key)
2. **Documentation**: `GET /api/n8n/docs`

## 🚨 Common Issues & Solutions

### Build Issues
- **ESLint Errors**: The build shows ESLint warnings but compiles successfully
- **Type Errors**: All TypeScript compilation errors have been resolved

### Runtime Issues
- **Environment Variables**: Ensure all required env vars are set in Vercel
- **API Keys**: Verify all API keys are valid and have proper permissions
- **CORS**: Update CORS settings for external services (Supabase, etc.)

### Performance
- **Cold Starts**: First request may be slower due to serverless cold starts
- **Vector Search**: Qdrant queries may take 1-2 seconds for large collections

## 📊 Monitoring

### Built-in Monitoring
- System health: `/api/status`
- Service status: `/dashboard/status`
- API documentation: `/api/n8n/docs`

### Vercel Analytics
- Enable Vercel Analytics for performance monitoring
- Monitor function execution times
- Track API endpoint usage

## 🔄 Updates & Maintenance

### Updating the Application
1. Push changes to your GitHub repository
2. Vercel will automatically redeploy
3. Monitor deployment logs for any issues
4. Test critical endpoints after deployment

### Environment Variable Updates
- Update in Vercel dashboard
- Redeploy if necessary
- Test affected functionality

## ✅ Deployment Checklist

- [ ] All environment variables configured in Vercel
- [ ] Google OAuth redirect URIs updated
- [ ] Supabase CORS settings updated
- [ ] Build completes successfully
- [ ] Health check endpoints respond correctly
- [ ] Authentication flow works
- [ ] RAG functionality tested
- [ ] n8n integration tested (if applicable)
- [ ] OpenAI-compatible endpoints tested

## 🎉 Success!

Your Odude Chat application is now deployed and ready to use! The application features:

- ✅ OpenAI-compatible API endpoints
- ✅ RAG-enhanced chatbot with Nepali-English support
- ✅ n8n integration for workflow automation
- ✅ Comprehensive testing and monitoring
- ✅ Easy AI provider switching capability

Visit your deployed application and start creating Q&A collections!
