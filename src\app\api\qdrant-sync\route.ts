import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { QdrantClient } from '@qdrant/js-client-rest';
import { createQdrantCollectionName } from '@/lib/qdrant-utils';

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Qdrant sync API called');

    // Check if Qdrant is configured
    if (!process.env.QDRANT_URL) {
      console.error('❌ QDRANT_URL not configured');
      return NextResponse.json(
        { error: 'Qdrant is not configured. Set QDRANT_URL environment variable.' },
        { status: 503 }
      );
    }

    console.log('✅ Qdrant URL configured:', process.env.QDRANT_URL);

    // Initialize Qdrant client inside the function
    const qdrantClient = new QdrantClient({
      url: process.env.QDRANT_URL,
      apiKey: process.env.QDRANT_API_KEY,
    });

    console.log('✅ Qdrant client initialized');

    const user = await getCurrentUser();
    console.log('✅ User authenticated:', user.id);

    const body = await request.json();
    console.log('✅ Request body parsed, keys:', Object.keys(body));
    const { question, answer, vector, collection_id, user_id, qa_pair_id } = body;

    console.log('🔄 Qdrant sync request:', {
      user_id,
      collection_id,
      question_length: question?.length,
      answer_length: answer?.length,
      vector_length: vector?.length,
      vector_type: typeof vector,
      is_array: Array.isArray(vector)
    });

    // Validate required fields
    if (!question || !answer || !vector || !collection_id || !user_id) {
      return NextResponse.json(
        { error: 'Missing required fields: question, answer, vector, collection_id, user_id' },
        { status: 400 }
      );
    }

    // Ensure the user can only sync their own data
    if (user.id !== user_id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Validate vector format
    if (!Array.isArray(vector) || vector.length === 0) {
      return NextResponse.json(
        { error: 'Vector must be a non-empty array' },
        { status: 400 }
      );
    }

    // Validate vector values
    const invalidValues = vector.filter(val => typeof val !== 'number' || !isFinite(val));
    if (invalidValues.length > 0) {
      return NextResponse.json(
        { error: `Vector contains invalid values: ${invalidValues.slice(0, 5)}` },
        { status: 400 }
      );
    }

    // Check for zero vectors (all zeros) which indicate failed embedding generation
    const isZeroVector = vector.every(val => val === 0);
    if (isZeroVector) {
      return NextResponse.json(
        { error: 'Vector appears to be a zero vector, indicating failed embedding generation. Please regenerate the embedding.' },
        { status: 400 }
      );
    }

    // Check vector magnitude (should not be too small for normalized vectors)
    const magnitude = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0));
    if (magnitude < 0.1) {
      console.warn(`⚠️ Vector has very small magnitude: ${magnitude.toFixed(6)}`);
      return NextResponse.json(
        { error: `Vector magnitude is too small (${magnitude.toFixed(6)}), indicating potential embedding generation issues.` },
        { status: 400 }
      );
    }

    // Check expected dimensions (Gemini text-embedding-004 produces 768-dimensional vectors)
    if (vector.length !== 768) {
      console.warn(`⚠️ Unexpected vector dimensions: ${vector.length}, expected 768 for Gemini text-embedding-004`);
    }

    // Create collection name for Qdrant (using user_id, collection_id, and vector dimensions)
    const qdrantCollectionName = createQdrantCollectionName(user_id, collection_id, vector.length);

    try {
      // Check if collection exists, create if it doesn't
      const collections = await qdrantClient.getCollections();
      const collectionExists = collections.collections?.some(
        (col) => col.name === qdrantCollectionName
      );

      if (!collectionExists) {
        console.log(`📊 Creating new Qdrant collection: ${qdrantCollectionName} with ${vector.length} dimensions`);
        await qdrantClient.createCollection(qdrantCollectionName, {
          vectors: {
            size: vector.length,
            distance: 'Cosine',
          },
        });
        console.log('✅ Collection created successfully');
      } else {
        console.log('✅ Collection already exists');
      }

      // Create a consistent point ID based on qa_pair_id if provided, otherwise use question hash
      // This ensures updates to the same QA pair use the same point ID
      let pointId;
      if (qa_pair_id) {
        // Use qa_pair_id for consistent point identification
        let simpleHash = 0;
        for (let i = 0; i < qa_pair_id.length; i++) {
          simpleHash = ((simpleHash << 5) - simpleHash + qa_pair_id.charCodeAt(i)) & 0x7fffffff;
        }
        pointId = Math.abs(simpleHash);
      } else {
        // Fallback to question-based hash for backward compatibility
        let simpleHash = 0;
        const combinedText = `${collection_id}_${question}`;
        for (let i = 0; i < combinedText.length; i++) {
          simpleHash = ((simpleHash << 5) - simpleHash + combinedText.charCodeAt(i)) & 0x7fffffff;
        }
        pointId = Math.abs(simpleHash);
      }

      // Validate point ID
      if (!pointId || pointId === 0) {
        return NextResponse.json(
          { error: 'Failed to generate valid point ID' },
          { status: 400 }
        );
      }

      console.log('📊 Preparing Qdrant upsert:', {
        pointId,
        vectorLength: vector.length,
        vectorType: typeof vector,
        isArray: Array.isArray(vector),
        firstFewValues: vector.slice(0, 3),
        collectionName: qdrantCollectionName
      });

      const upsertData = {
        wait: true,
        points: [
          {
            id: pointId,
            vector: vector,
            payload: {
              question,
              answer,
              collection_id,
              user_id,
              created_at: new Date().toISOString(),
            },
          },
        ],
      };

      console.log('📊 Upsert data structure (without vector):', JSON.stringify({
        ...upsertData,
        points: upsertData.points.map(point => ({
          ...point,
          vector: `[${point.vector.length} dimensions]`
        }))
      }, null, 2));

      // Upsert the vector point
      await qdrantClient.upsert(qdrantCollectionName, upsertData);

      return NextResponse.json({
        success: true,
        message: 'Vector synced to Qdrant successfully',
        pointId,
        collectionName: qdrantCollectionName,
      });

    } catch (qdrantError) {
      console.error('Qdrant operation failed:', qdrantError);
      return NextResponse.json(
        { error: 'Failed to sync with Qdrant', details: qdrantError instanceof Error ? qdrantError.message : 'Unknown error' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in qdrant-sync API:', error);
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace');
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}
