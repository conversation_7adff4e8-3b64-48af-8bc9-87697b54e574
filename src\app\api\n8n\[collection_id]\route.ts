import { NextRequest, NextResponse } from 'next/server';
import { QdrantClient } from '@qdrant/js-client-rest';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { APP_CONFIG } from '@/lib/config';
import { getQdrantCollectionName } from '@/lib/qdrant-utils';
import { supabase } from '@/lib/supabase';
import { generateSearchEmbedding } from '@/lib/embedding-client';
import {
  detectQuestionPattern,
  getCachedResponse,
  cacheResponse,
  shouldCacheQuestion
} from '@/lib/question-cache';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ collection_id: string }> }
) {
  try {
    const { collection_id } = await params;
    
    console.log('🤖 Collection-specific n8n request:', {
      collection_id,
      timestamp: new Date().toISOString()
    });

    // Validate collection_id
    if (!collection_id) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Collection ID is required',
          code: 'MISSING_COLLECTION_ID'
        },
        { status: 400 }
      );
    }

    // Check if required services are configured
    if (!process.env.QDRANT_URL) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Vector search service is not available',
          code: 'QDRANT_NOT_CONFIGURED'
        },
        { status: 503 }
      );
    }

    if (!process.env.GEMINI_API_KEY) {
      return NextResponse.json(
        { 
          success: false,
          error: 'AI service is not available',
          code: 'GEMINI_NOT_CONFIGURED'
        },
        { status: 503 }
      );
    }

    // Get request body
    const body = await request.json();
    const { 
      question, 
      query,
      language = APP_CONFIG.rag.defaultLanguage,
      limit = APP_CONFIG.rag.maxSearchResults,
      threshold = APP_CONFIG.rag.similarityThreshold 
    } = body;

    // Use either question or query
    const searchQuery = question || query;
    
    if (!searchQuery) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Question or query is required',
          code: 'MISSING_QUERY',
          required_fields: ['question', 'query']
        },
        { status: 400 }
      );
    }

    // Validate language
    const supportedLanguages = APP_CONFIG.rag.supportedLanguages.map(lang => lang.code);
    if (!supportedLanguages.includes(language)) {
      return NextResponse.json(
        { 
          success: false,
          error: `Unsupported language. Supported: ${supportedLanguages.join(', ')}`,
          code: 'UNSUPPORTED_LANGUAGE',
          supported_languages: supportedLanguages
        },
        { status: 400 }
      );
    }

    // Verify collection exists in database (public collections only)
    const { data: collection, error: collectionError } = await supabase
      .from('collections')
      .select('id, name, user_id, description, sales_instructions')
      .eq('id', collection_id)
      .single();

    if (collectionError || !collection) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Collection not found or not accessible',
          code: 'COLLECTION_NOT_FOUND',
          collection_id
        },
        { status: 404 }
      );
    }

    console.log('🤖 n8n chat request:', {
      collection_id,
      question_length: searchQuery.length,
      language,
    });

    // Check for cached response for common questions
    const questionPattern = detectQuestionPattern(searchQuery);
    if (questionPattern) {
      const cachedResponse = getCachedResponse(collection_id, questionPattern, language);
      if (cachedResponse) {
        console.log('✅ Returning cached response for pattern:', questionPattern);
        return NextResponse.json({
          success: true,
          answer: cachedResponse.answer,
          question: searchQuery,
          language,
          cached: true,
          cache_info: {
            pattern: questionPattern,
            hit_count: cachedResponse.hitCount,
            cached_at: new Date(cachedResponse.timestamp).toISOString()
          },
          context: {
            results_found: 0,
            search_results: [],
            has_context: false,
            note: 'Response served from cache'
          },
          metadata: {
            collection_id,
            collection_name: collection.name,
            collection_description: collection.description,
            search_params: { limit, threshold },
            model: APP_CONFIG.api.chat.model,
            timestamp: new Date().toISOString(),
          },
        });
      }
    }

    // Generate embedding for the search query
    let searchVector: number[];
    try {
      // Get the base URL for internal API calls
      const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';

      // Try Gemini embedding first
      const embeddingResponse = await fetch(`${baseUrl}/api/generate-gemini-embedding`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.INTERNAL_API_TOKEN || 'internal'}`, // Internal token for server-to-server calls
        },
        body: JSON.stringify({
          query: searchQuery,
          type: 'search_query'
        }),
      });

      if (!embeddingResponse.ok) {
        // Fallback to OpenRouter if available
        if (process.env.OPENROUTER_API_KEY) {
          const openrouterResponse = await fetch(`${baseUrl}/api/generate-openrouter-embedding`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${process.env.INTERNAL_API_TOKEN || 'internal'}`,
            },
            body: JSON.stringify({
              query: searchQuery
            }),
          });

          if (openrouterResponse.ok) {
            const openrouterResult = await openrouterResponse.json();
            searchVector = openrouterResult.embedding;
          } else {
            throw new Error('Both Gemini and OpenRouter embedding generation failed');
          }
        } else {
          throw new Error('Embedding generation failed and no fallback available');
        }
      } else {
        const embeddingResult = await embeddingResponse.json();
        searchVector = embeddingResult.embedding;
      }
    } catch (embeddingError) {
      console.error('❌ Embedding generation failed:', embeddingError);
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to process query',
          code: 'EMBEDDING_GENERATION_FAILED',
          details: embeddingError instanceof Error ? embeddingError.message : 'Unknown error'
        },
        { status: 500 }
      );
    }

    // Initialize Qdrant client
    const qdrantClient = new QdrantClient({
      url: process.env.QDRANT_URL,
      apiKey: process.env.QDRANT_API_KEY,
    });

    // Find the correct collection name in Qdrant
    const qdrantCollectionName = await getQdrantCollectionName(
      qdrantClient,
      collection.user_id,
      collection_id,
      searchVector?.length
    );

    if (!qdrantCollectionName) {
      return NextResponse.json({
        success: false,
        error: 'Collection not found. Please ensure the collection exists and has Q&A pairs.',
        code: 'COLLECTION_NOT_FOUND',
        collection_name: collection.name,
        collection_id,
        details: 'The collection exists in the database but has no Q&A pairs synced to the vector database. Please add Q&A pairs and ensure they are synced.'
      }, { status: 404 });
    }

    // Perform vector search
    const searchResults = await qdrantClient.search(qdrantCollectionName, {
      vector: searchVector,
      limit: limit,
      score_threshold: threshold,
      with_payload: true,
    });

    console.log('🔍 Vector search completed:', {
      collection_id,
      results_count: searchResults.length,
      top_score: searchResults[0]?.score || 0,
    });

    // If no results found, return empty response
    if (searchResults.length === 0) {
      return NextResponse.json({
        success: true,
        answer: "I couldn't find any relevant information in this collection to answer your question.",
        question: searchQuery,
        language,
        context: {
          results_found: 0,
          search_results: [],
          has_context: false,
        },
        metadata: {
          collection_id,
          collection_name: collection.name,
          search_params: { limit, threshold },
          timestamp: new Date().toISOString(),
        },
      });
    }

    // Prepare context for RAG
    const context = searchResults.map(result => 
      `Q: ${result.payload?.question}\nA: ${result.payload?.answer}`
    ).join('\n\n');

    // Generate AI response using Gemini
    const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
    const model = genAI.getGenerativeModel({ model: APP_CONFIG.api.chat.model });

    // Create language-specific prompt
    const languageInstructions = {
      'en': 'Respond in clear English.',
      'hi-en': 'You can respond in a mix of Hindi and English (Hinglish) as appropriate.',
      'ne-en': 'You can respond in a mix of Nepali and English as appropriate.'
    };

    // Build the prompt with sales instructions if available
    let basePrompt = `Based on the following Q&A context, answer the user's question. ${languageInstructions[language as keyof typeof languageInstructions] || languageInstructions.en}`;

    // Add sales instructions if provided
    if (collection.sales_instructions && collection.sales_instructions.trim()) {
      basePrompt += `\n\nAdditional Instructions: ${collection.sales_instructions.trim()}`;
    }

    const prompt = `${basePrompt}

Context:
${context}

Question: ${searchQuery}

Answer:`;

    const result = await model.generateContent(prompt);
    const answer = result.response.text();

    console.log('✅ RAG response generated:', {
      collection_id,
      question_length: searchQuery.length,
      answer_length: answer.length,
      context_results: searchResults.length,
      language
    });

    // Cache the response if it's a common question pattern
    if (questionPattern && shouldCacheQuestion(searchQuery)) {
      cacheResponse(collection_id, questionPattern, language, answer);
      console.log('💾 Cached response for pattern:', questionPattern);
    }

    return NextResponse.json({
      success: true,
      answer,
      question: searchQuery,
      language,
      context: {
        results_found: searchResults.length,
        search_results: searchResults.map(result => ({
          id: result.id,
          score: result.score,
          question: result.payload?.question,
          answer: result.payload?.answer,
        })),
        has_context: context.length > 0,
      },
      metadata: {
        collection_id,
        collection_name: collection.name,
        collection_description: collection.description,
        search_params: { limit, threshold },
        model: APP_CONFIG.api.chat.model,
        timestamp: new Date().toISOString(),
      },
    });

  } catch (error) {
    console.error('❌ Collection-specific n8n endpoint failed:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

// GET endpoint for collection information
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ collection_id: string }> }
) {
  try {
    const { collection_id } = await params;

    // Get collection information
    const { data: collection, error: collectionError } = await supabase
      .from('collections')
      .select('id, name, description, sales_instructions, created_at')
      .eq('id', collection_id)
      .single();

    if (collectionError || !collection) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Collection not found',
          code: 'COLLECTION_NOT_FOUND',
          collection_id
        },
        { status: 404 }
      );
    }

    // Get Q&A count
    const { count, error: countError } = await supabase
      .from('qa_pairs')
      .select('*', { count: 'exact', head: true })
      .eq('collection_id', collection_id);

    return NextResponse.json({
      success: true,
      collection: {
        id: collection.id,
        name: collection.name,
        description: collection.description,
        created_at: collection.created_at,
        qa_count: count || 0
      },
      endpoints: {
        'POST': `Ask questions to this collection`,
        'GET': `Get collection information`
      },
      usage: {
        method: 'POST',
        body: {
          question: 'Your question here',
          language: 'en|hi-en|ne-en (optional)',
          limit: 'number (optional, default: 5)',
          threshold: 'number (optional, default: 0.7)'
        }
      },
      supported_languages: APP_CONFIG.rag.supportedLanguages,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Collection info endpoint failed:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
