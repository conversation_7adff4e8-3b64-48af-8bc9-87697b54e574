import { NextResponse } from 'next/server';
import { APP_CONFIG } from '@/lib/config';

export async function GET() {
  return NextResponse.json({
    name: 'Odude Chat API Configuration',
    version: '1.0.0',
    description: 'OpenAI-compatible API configuration and provider information',
    
    // Current AI provider configuration
    ai_provider: {
      chat: {
        provider: APP_CONFIG.api.chat.provider,
        model: APP_CONFIG.api.chat.model,
        max_tokens: APP_CONFIG.api.chat.maxTokens,
        temperature: APP_CONFIG.api.chat.temperature,
        status: process.env.GEMINI_API_KEY ? 'configured' : 'not_configured'
      },
      embedding: {
        provider: APP_CONFIG.api.embedding.provider,
        model: APP_CONFIG.api.embedding.model,
        features: APP_CONFIG.api.embedding.features,
        status: process.env.GEMINI_API_KEY ? 'configured' : 'not_configured'
      }
    },

    // Available endpoints
    endpoints: {
      openai_compatible: {
        'POST /api/v1/chat/completions': {
          description: 'Standard OpenAI-compatible chat completions',
          authentication: 'Required (NextAuth session)',
          features: ['Multi-provider support', 'Temperature control', 'Token limits']
        },
        'POST /api/v1/chat/rag-completions': {
          description: 'RAG-enhanced chat completions using Q&A collections',
          authentication: 'Required (NextAuth session)',
          features: ['Vector search', 'Context injection', 'Multilingual support']
        }
      },
      legacy: {
        'POST /api/rag-chat': {
          description: 'Legacy RAG chat endpoint',
          authentication: 'Required (NextAuth session)',
          status: 'maintained'
        }
      },
      n8n_integration: {
        'POST /api/n8n/rag-chat': {
          description: 'n8n-compatible RAG chat endpoint',
          authentication: 'API key required',
          features: ['Public access', 'Workflow integration']
        }
      }
    },

    // Language support
    languages: {
      supported: APP_CONFIG.rag.supportedLanguages,
      default: APP_CONFIG.rag.defaultLanguage,
      features: [
        'Nepali-English mixed responses',
        'Hindi-English mixed responses', 
        'WhatsApp-style short messages',
        'Sales-oriented persona'
      ]
    },

    // RAG configuration
    rag: {
      max_search_results: APP_CONFIG.rag.maxSearchResults,
      similarity_threshold: APP_CONFIG.rag.similarityThreshold,
      vector_database: process.env.QDRANT_URL ? 'configured' : 'not_configured',
      embedding_dimensions: 'dynamic (based on provider)'
    },

    // Provider switching guide
    provider_switching: {
      description: 'How to switch between AI providers',
      steps: [
        '1. Update src/lib/config.ts with new provider settings',
        '2. Set appropriate environment variables (OPENAI_API_KEY, etc.)',
        '3. Restart the application',
        '4. Test with /api/v1/chat/completions endpoint'
      ],
      supported_providers: [
        {
          name: 'gemini',
          models: ['gemini-1.5-flash', 'gemini-1.5-pro'],
          env_vars: ['GEMINI_API_KEY'],
          status: 'active'
        },
        {
          name: 'openai',
          models: ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo'],
          env_vars: ['OPENAI_API_KEY'],
          status: 'supported'
        }
      ]
    },

    // Example configurations
    examples: {
      switch_to_openai: {
        config_update: {
          'api.chat.provider': 'openai',
          'api.chat.model': 'gpt-4',
          'api.chat.apiKey': 'process.env.OPENAI_API_KEY'
        },
        env_vars: {
          'OPENAI_API_KEY': 'your-openai-api-key'
        }
      },
      openai_compatible_request: {
        url: '/api/v1/chat/completions',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: {
          model: 'gemini-1.5-flash',
          messages: [
            {
              role: 'system',
              content: 'You are a helpful assistant.'
            },
            {
              role: 'user', 
              content: 'Hello, how are you?'
            }
          ],
          temperature: 0.7,
          max_tokens: 1000
        }
      },
      rag_request: {
        url: '/api/v1/chat/rag-completions',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: {
          model: 'gemini-1.5-flash',
          collection_id: 'your-collection-id',
          language: 'ne-en',
          messages: [
            {
              role: 'user',
              content: 'What is the quality of the material?'
            }
          ],
          temperature: 0.7,
          max_tokens: 1000,
          limit: 5,
          threshold: 0.5
        }
      }
    }
  });
}
