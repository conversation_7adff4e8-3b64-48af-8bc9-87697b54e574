import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { QdrantClient } from '@qdrant/js-client-rest';

export async function DELETE(request: NextRequest) {
  try {
    // Check if Qdrant is configured
    if (!process.env.QDRANT_URL) {
      return NextResponse.json(
        { error: 'Qdrant is not configured. Set QDRANT_URL environment variable.' },
        { status: 503 }
      );
    }

    // Initialize Qdrant client inside the function
    const qdrantClient = new QdrantClient({
      url: process.env.QDRANT_URL,
      apiKey: process.env.QDRANT_API_KEY,
    });

    const user = await getCurrentUser();
    
    const body = await request.json();
    const { question, collection_id, user_id, qa_pair_id } = body;

    // Validate required fields
    if (!question || !collection_id || !user_id) {
      return NextResponse.json(
        { error: 'Missing required fields: question, collection_id, user_id' },
        { status: 400 }
      );
    }

    // Ensure the user can only delete their own data
    if (user.id !== user_id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Create collection name for Qdrant (using user_id and collection_id)
    const qdrantCollectionName = `user_${user_id}_collection_${collection_id}`;

    try {
      // Check if collection exists
      const collections = await qdrantClient.getCollections();
      const collectionExists = collections.collections?.some(
        (col) => col.name === qdrantCollectionName
      );

      if (!collectionExists) {
        return NextResponse.json({
          success: true,
          message: 'Qdrant collection does not exist, nothing to delete',
        });
      }

      // Create the same point ID that was used during creation
      // Must match the point ID generation logic in qdrant-sync
      let pointId;
      if (qa_pair_id) {
        // Use qa_pair_id for consistent point identification (preferred method)
        let simpleHash = 0;
        for (let i = 0; i < qa_pair_id.length; i++) {
          simpleHash = ((simpleHash << 5) - simpleHash + qa_pair_id.charCodeAt(i)) & 0x7fffffff;
        }
        pointId = Math.abs(simpleHash);
      } else {
        // Fallback to question-based hash for backward compatibility
        let simpleHash = 0;
        const combinedText = `${collection_id}_${question}`;
        for (let i = 0; i < combinedText.length; i++) {
          simpleHash = ((simpleHash << 5) - simpleHash + combinedText.charCodeAt(i)) & 0x7fffffff;
        }
        pointId = Math.abs(simpleHash);
      }

      // Delete the vector point
      await qdrantClient.delete(qdrantCollectionName, {
        wait: true,
        points: [pointId],
      });

      return NextResponse.json({
        success: true,
        message: 'Vector deleted from Qdrant successfully',
        pointId,
        collectionName: qdrantCollectionName,
      });

    } catch (qdrantError) {
      console.error('Qdrant delete operation failed:', qdrantError);
      
      // Don't fail the entire operation if Qdrant delete fails
      // This allows the database deletion to proceed even if vector deletion fails
      return NextResponse.json({
        success: false,
        error: 'Failed to delete from Qdrant',
        details: qdrantError instanceof Error ? qdrantError.message : 'Unknown error',
        warning: 'Database record can still be deleted even if vector deletion fails'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Error in qdrant-delete API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
