import { GoogleGenerativeAI } from '@google/generative-ai';
import { APP_CONFIG } from './config';

// OpenAI-compatible interfaces
export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface ChatCompletionRequest {
  model: string;
  messages: ChatMessage[];
  temperature?: number;
  max_tokens?: number;
  stream?: boolean;
}

export interface ChatCompletionResponse {
  id: string;
  object: 'chat.completion';
  created: number;
  model: string;
  choices: {
    index: number;
    message: ChatMessage;
    finish_reason: 'stop' | 'length' | 'content_filter';
  }[];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// Abstract AI provider interface
export interface AIProvider {
  generateChatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse>;
  generateContent(prompt: string): Promise<string>;
}

// Gemini provider implementation
export class GeminiProvider implements AIProvider {
  private genAI: GoogleGenerativeAI;
  private model: any;

  constructor(apiKey: string, modelName: string) {
    this.genAI = new GoogleGenerativeAI(apiKey);
    this.model = this.genAI.getGenerativeModel({ model: modelName });
  }

  async generateChatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
    // Convert OpenAI messages to Gemini format
    const prompt = this.convertMessagesToPrompt(request.messages);
    
    const result = await this.model.generateContent(prompt);
    const response = result.response;
    const text = response.text();

    // Convert Gemini response to OpenAI format
    return {
      id: `chatcmpl-${Date.now()}`,
      object: 'chat.completion',
      created: Math.floor(Date.now() / 1000),
      model: request.model,
      choices: [{
        index: 0,
        message: {
          role: 'assistant',
          content: text
        },
        finish_reason: 'stop'
      }],
      usage: {
        prompt_tokens: this.estimateTokens(prompt),
        completion_tokens: this.estimateTokens(text),
        total_tokens: this.estimateTokens(prompt) + this.estimateTokens(text)
      }
    };
  }

  async generateContent(prompt: string): Promise<string> {
    const result = await this.model.generateContent(prompt);
    const response = result.response;
    return response.text();
  }

  private convertMessagesToPrompt(messages: ChatMessage[]): string {
    return messages.map(msg => {
      switch (msg.role) {
        case 'system':
          return `System: ${msg.content}`;
        case 'user':
          return `User: ${msg.content}`;
        case 'assistant':
          return `Assistant: ${msg.content}`;
        default:
          return msg.content;
      }
    }).join('\n\n');
  }

  private estimateTokens(text: string): number {
    // Rough estimation: ~4 characters per token
    return Math.ceil(text.length / 4);
  }
}

// OpenAI provider implementation (for future use)
export class OpenAIProvider implements AIProvider {
  private apiKey: string;
  private baseURL: string;

  constructor(apiKey: string, baseURL = 'https://api.openai.com/v1') {
    this.apiKey = apiKey;
    this.baseURL = baseURL;
  }

  async generateChatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
    const response = await fetch(`${this.baseURL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.statusText}`);
    }

    return response.json();
  }

  async generateContent(prompt: string): Promise<string> {
    const request: ChatCompletionRequest = {
      model: 'gpt-3.5-turbo',
      messages: [{ role: 'user', content: prompt }],
    };

    const response = await this.generateChatCompletion(request);
    return response.choices[0]?.message.content || '';
  }
}

// Factory function to create AI provider based on configuration
export function createAIProvider(): AIProvider {
  const config = APP_CONFIG.api.chat;
  
  switch (config.provider) {
    case 'gemini':
      if (!process.env.GEMINI_API_KEY) {
        throw new Error('GEMINI_API_KEY environment variable is required');
      }
      return new GeminiProvider(process.env.GEMINI_API_KEY, config.model);
    
    case 'openai':
      if (!process.env.OPENAI_API_KEY) {
        throw new Error('OPENAI_API_KEY environment variable is required');
      }
      return new OpenAIProvider(process.env.OPENAI_API_KEY);
    
    default:
      throw new Error(`Unsupported AI provider: ${config.provider}`);
  }
}

// Utility function to create language-specific system prompts
export function createLanguagePrompt(language: string): string {
  const languageInstructions = {
    'en': 'Respond in clear English.',
    'hi-en': 'You can respond in a mix of Hindi and English (Hinglish) as appropriate. Use WhatsApp-style short messages and ask relevant questions to encourage purchases.',
    'ne-en': 'You can respond in a mix of Nepali and English as appropriate. Use WhatsApp-style short messages and ask relevant questions to encourage purchases.'
  };

  return languageInstructions[language as keyof typeof languageInstructions] || languageInstructions['en'];
}
