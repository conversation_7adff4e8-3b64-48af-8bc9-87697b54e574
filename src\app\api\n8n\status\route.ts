import { NextRequest, NextResponse } from 'next/server';
import { QdrantClient } from '@qdrant/js-client-rest';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { APP_CONFIG } from '@/lib/config';
import { getCacheStats, clearExpiredCache } from '@/lib/question-cache';

// Simple API key authentication for n8n
function validateApi<PERSON>ey(request: NextRequest): boolean {
  const apiKey = request.headers.get('x-api-key') || request.headers.get('authorization')?.replace('Bearer ', '');
  
  // For now, we'll use a simple API key check
  // In production, you should use a proper API key management system
  const validApiKey = process.env.N8N_API_KEY || 'your-secret-api-key';
  
  return apiKey === validApiKey;
}

export async function GET(request: NextRequest) {
  try {
    // Validate API key for n8n access
    if (!validateApiKey(request)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Unauthorized. Please provide a valid API key in the x-api-key header or Authorization header.',
          status: 'unauthorized'
        },
        { status: 401 }
      );
    }

    const checks = {
      qdrant: { status: 'unknown', message: '', details: {} },
      gemini: { status: 'unknown', message: '', details: {} },
      overall: { status: 'unknown', message: '' }
    };

    // Check Qdrant connection
    if (!process.env.QDRANT_URL) {
      checks.qdrant = {
        status: 'warning',
        message: 'QDRANT_URL not configured',
        details: {
          note: 'Vector search will not be available',
          required_env: 'QDRANT_URL'
        }
      };
    } else {
      try {
        const qdrantClient = new QdrantClient({
          url: process.env.QDRANT_URL,
          apiKey: process.env.QDRANT_API_KEY,
        });

        const collections = await qdrantClient.getCollections();
        checks.qdrant = {
          status: 'healthy',
          message: 'Connected successfully',
          details: {
            url: process.env.QDRANT_URL,
            hasApiKey: !!process.env.QDRANT_API_KEY,
            collections: collections.collections?.length || 0
          }
        };
      } catch (error) {
        checks.qdrant = {
          status: 'error',
          message: 'Connection failed',
          details: {
            error: error instanceof Error ? error.message : 'Unknown error'
          }
        };
      }
    }

    // Check Gemini API
    if (!process.env.GEMINI_API_KEY) {
      checks.gemini = {
        status: 'warning',
        message: 'GEMINI_API_KEY not configured',
        details: {
          note: 'RAG chat functionality will not be available',
          required_env: 'GEMINI_API_KEY'
        }
      };
    } else {
      try {
        const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
        const model = genAI.getGenerativeModel({ model: APP_CONFIG.api.chat.model });
        
        // Simple test to verify API key works
        const testResult = await model.generateContent('Hello');
        const testResponse = testResult.response.text();
        
        checks.gemini = {
          status: 'healthy',
          message: 'API key valid and working',
          details: {
            model: APP_CONFIG.api.chat.model,
            test_response_length: testResponse.length
          }
        };
      } catch (error) {
        checks.gemini = {
          status: 'error',
          message: 'API key invalid or service unavailable',
          details: {
            error: error instanceof Error ? error.message : 'Unknown error'
          }
        };
      }
    }

    // Determine overall status
    const hasErrors = Object.values(checks).some(check => check.status === 'error');
    const hasWarnings = Object.values(checks).some(check => check.status === 'warning');
    
    if (hasErrors) {
      checks.overall = {
        status: 'error',
        message: 'Some services are not available'
      };
    } else if (hasWarnings) {
      checks.overall = {
        status: 'warning',
        message: 'Some optional services are not configured'
      };
    } else {
      checks.overall = {
        status: 'healthy',
        message: 'All services are operational'
      };
    }

    // Get cache statistics
    const cacheStats = getCacheStats();
    const expiredCleared = clearExpiredCache();

    return NextResponse.json({
      success: true,
      status: checks.overall.status,
      message: checks.overall.message,
      services: {
        qdrant: checks.qdrant,
        gemini: checks.gemini
      },
      endpoints: {
        'POST /api/n8n/rag-chat': 'Generate AI responses based on Q&A collection context',
        'POST /api/n8n/rag-search': 'Search for similar Q&A pairs using vector similarity',
        'GET /api/n8n/status': 'Check service status and availability'
      },
      supported_languages: APP_CONFIG.rag.supportedLanguages,
      api_info: {
        authentication: 'API key required in x-api-key header or Authorization header',
        rate_limits: 'None currently implemented',
        version: '1.0.0'
      },
      cache_performance: {
        total_entries: cacheStats.totalEntries,
        expired_cleared: expiredCleared,
        embedding_calls_saved: Object.values(cacheStats.hitCounts).reduce((sum, count) => sum + (count - 1), 0),
        estimated_cost_savings: `$${(Object.values(cacheStats.hitCounts).reduce((sum, count) => sum + (count - 1), 0) * 0.001).toFixed(3)}`,
        cached_patterns: cacheStats.patterns.map(p => p.cacheKey)
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in n8n status API:', error);
    return NextResponse.json(
      { 
        success: false,
        status: 'error',
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
