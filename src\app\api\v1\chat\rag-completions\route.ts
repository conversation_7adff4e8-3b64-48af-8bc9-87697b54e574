import { NextRequest, NextResponse } from 'next/server';
import { QdrantClient } from '@qdrant/js-client-rest';
import { createAIProvider, createLanguagePrompt, type ChatMessage } from '@/lib/ai-providers';
import { getCurrentUser } from '@/lib/auth';
import { generateSearchEmbedding } from '@/lib/embedding-client';
import { getQdrantCollectionName } from '@/lib/qdrant-utils';
import { APP_CONFIG } from '@/lib/config';

interface RAGCompletionRequest {
  model?: string;
  messages: ChatMessage[];
  collection_id: string;
  temperature?: number;
  max_tokens?: number;
  language?: string;
  limit?: number;
  threshold?: number;
}

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const user = await getCurrentUser();
    
    const body: RAGCompletionRequest = await request.json();
    
    // Validate required fields
    if (!body.messages || !Array.isArray(body.messages) || body.messages.length === 0) {
      return NextResponse.json(
        { 
          error: {
            message: 'Missing required field: messages',
            type: 'invalid_request_error',
            code: 'missing_required_parameter'
          }
        },
        { status: 400 }
      );
    }

    if (!body.collection_id) {
      return NextResponse.json(
        { 
          error: {
            message: 'Missing required field: collection_id',
            type: 'invalid_request_error',
            code: 'missing_required_parameter'
          }
        },
        { status: 400 }
      );
    }

    // Set defaults
    const language = body.language || APP_CONFIG.rag.defaultLanguage;
    const limit = body.limit || APP_CONFIG.rag.maxSearchResults;
    const threshold = body.threshold || APP_CONFIG.rag.similarityThreshold;

    console.log('🤖 RAG-enhanced chat completion request:', {
      user_id: user.id,
      collection_id: body.collection_id,
      messages_count: body.messages.length,
      language,
      limit,
      threshold
    });

    // Get the last user message for RAG search
    const userMessages = body.messages.filter(msg => msg.role === 'user');
    const lastUserMessage = userMessages[userMessages.length - 1];
    
    if (!lastUserMessage) {
      return NextResponse.json(
        { 
          error: {
            message: 'No user message found for RAG search',
            type: 'invalid_request_error',
            code: 'no_user_message'
          }
        },
        { status: 400 }
      );
    }

    // Initialize Qdrant client
    if (!process.env.QDRANT_URL) {
      return NextResponse.json(
        { 
          error: {
            message: 'Vector search service not configured',
            type: 'server_error',
            code: 'service_unavailable'
          }
        },
        { status: 503 }
      );
    }

    const qdrantClient = new QdrantClient({
      url: process.env.QDRANT_URL,
      apiKey: process.env.QDRANT_API_KEY,
    });

    // Generate embedding for the user's question
    const queryVector = await generateSearchEmbedding(lastUserMessage.content);

    // Find the correct collection name
    const qdrantCollectionName = await getQdrantCollectionName(
      qdrantClient,
      user.id,
      body.collection_id,
      queryVector.length
    );

    if (!qdrantCollectionName) {
      return NextResponse.json(
        { 
          error: {
            message: 'Collection not found or empty',
            type: 'invalid_request_error',
            code: 'collection_not_found'
          }
        },
        { status: 404 }
      );
    }

    // Perform vector search
    const searchResults = await qdrantClient.search(qdrantCollectionName, {
      vector: queryVector,
      limit: limit,
      score_threshold: threshold,
      with_payload: true,
    });

    console.log('🔍 Vector search completed:', {
      results_count: searchResults.length,
      top_score: searchResults[0]?.score || 0,
    });

    // Prepare context from search results
    const context = searchResults.map(result => 
      `Q: ${result.payload?.question}\nA: ${result.payload?.answer}`
    ).join('\n\n');

    // Create enhanced messages with RAG context
    const enhancedMessages: ChatMessage[] = [
      {
        role: 'system',
        content: `You are a helpful assistant with access to a knowledge base. ${createLanguagePrompt(language)}

${context ? `Here is relevant context from the knowledge base:

${context}

Please use this context to provide accurate and helpful responses. If the context doesn't contain relevant information, you can provide general assistance.` : 'No specific context found in the knowledge base for this query.'}`
      },
      ...body.messages
    ];

    // Generate response using AI provider
    const aiProvider = createAIProvider();
    const response = await aiProvider.generateChatCompletion({
      model: body.model || APP_CONFIG.api.chat.model,
      messages: enhancedMessages,
      temperature: body.temperature ?? 0.7,
      max_tokens: body.max_tokens ?? 1000
    });

    // Add RAG metadata to the response
    const ragResponse = {
      ...response,
      rag_metadata: {
        collection_id: body.collection_id,
        search_results_count: searchResults.length,
        context_used: context.length > 0,
        language: language,
        search_params: { limit, threshold },
        search_results: searchResults.map(result => ({
          id: result.id,
          score: result.score,
          question: result.payload?.question,
          answer: result.payload?.answer
        }))
      }
    };

    console.log('✅ RAG chat completion generated:', {
      response_length: response.choices[0]?.message.content.length || 0,
      context_results: searchResults.length,
      language: language
    });

    return NextResponse.json(ragResponse);

  } catch (error) {
    console.error('❌ RAG chat completion error:', error);

    if (error instanceof Error) {
      if (error.message.includes('API key')) {
        return NextResponse.json(
          { 
            error: {
              message: 'AI service unavailable or invalid API key',
              type: 'authentication_error',
              code: 'invalid_api_key'
            }
          },
          { status: 401 }
        );
      }

      if (error.message.includes('embedding')) {
        return NextResponse.json(
          { 
            error: {
              message: 'Failed to generate embedding for search',
              type: 'server_error',
              code: 'embedding_error'
            }
          },
          { status: 500 }
        );
      }
    }

    return NextResponse.json(
      { 
        error: {
          message: 'Internal server error',
          type: 'server_error',
          code: 'internal_error'
        }
      },
      { status: 500 }
    );
  }
}

// GET endpoint for API information
export async function GET() {
  return NextResponse.json({
    name: 'RAG-Enhanced Chat Completions API',
    version: '1.0.0',
    description: 'OpenAI-compatible chat completions with RAG (Retrieval-Augmented Generation) using your Q&A collections',
    endpoint: '/api/v1/chat/rag-completions',
    methods: ['POST'],
    authentication: 'Required (NextAuth session)',
    features: [
      'OpenAI-compatible interface',
      'RAG-enhanced responses using Q&A collections',
      'Multilingual support (English, Hindi-English, Nepali-English)',
      'Vector similarity search',
      'Context-aware responses'
    ],
    example_request: {
      model: 'gemini-1.5-flash',
      collection_id: 'your-collection-id',
      language: 'ne-en',
      messages: [
        {
          role: 'user',
          content: 'What is the quality of the material?'
        }
      ],
      temperature: 0.7,
      max_tokens: 1000,
      limit: 5,
      threshold: 0.5
    }
  });
}
