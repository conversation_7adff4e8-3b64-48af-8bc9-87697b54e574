import { NextRequest, NextResponse } from 'next/server';
import { getCacheStats, clearExpiredCache, clearAllCache } from '@/lib/question-cache';

export async function GET(request: NextRequest) {
  try {
    const stats = getCacheStats();
    const cleared = clearExpiredCache(); // Clean up expired entries
    
    return NextResponse.json({
      success: true,
      cache_stats: {
        total_entries: stats.totalEntries,
        expired_cleared: cleared,
        hit_counts: stats.hitCounts,
        patterns: stats.patterns
      },
      benefits: {
        embedding_calls_saved: Object.values(stats.hitCounts).reduce((sum, count) => sum + (count - 1), 0),
        estimated_cost_savings: `$${(Object.values(stats.hitCounts).reduce((sum, count) => sum + (count - 1), 0) * 0.001).toFixed(3)}`,
        response_time_improvement: 'Instant (no embedding generation or vector search needed)'
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting cache stats:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to get cache statistics',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const clearAll = url.searchParams.get('all') === 'true';
    
    if (clearAll) {
      clearAllCache();
      return NextResponse.json({
        success: true,
        message: 'All cache entries cleared',
        timestamp: new Date().toISOString()
      });
    } else {
      const cleared = clearExpiredCache();
      return NextResponse.json({
        success: true,
        message: `${cleared} expired cache entries cleared`,
        cleared_count: cleared,
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('Error clearing cache:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to clear cache',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
